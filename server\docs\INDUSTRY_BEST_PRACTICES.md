# 业界日志系统最佳实践

## 主流开源项目的日志设计

### 1. Uber Zap（性能最佳）

**特点：**
- 零内存分配的结构化日志
- 强类型字段
- 高性能

```go
// 中间件
func ZapMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        logger := zap.L().With(
            zap.String("request_id", uuid.New().String()),
            zap.String("method", c.Request.Method),
        )
        c.Set("logger", logger)
        c.Next()
    }
}

// 使用
logger := c.MustGet("logger").(*zap.Logger)
logger.Info("user login",
    zap.String("user_id", userID),
    zap.Duration("duration", time.Since(start)),
)
```

### 2. Kubernetes klog

**特点：**
- 结构化日志
- 对象引用
- 分级日志

```go
// 使用方式
klog.InfoS("Pod created", 
    "pod", klog.KObj(pod),
    "namespace", pod.Namespace,
)

// 带上下文
logger := klog.FromContext(ctx)
logger.Info("processing", "name", pod.Name)
```

### 3. Go 1.21+ slog（官方标准）

**特点：**
- 官方标准库
- 结构化日志
- 性能优化

```go
// 创建带上下文的日志器
logger := slog.With("request_id", requestID)

// 使用
logger.Info("processing request", 
    "user_id", userID,
    "action", "login",
)
```

### 4. Grafana 风格

**特点：**
- 组件化日志器
- 链式调用
- 上下文传递

```go
logger := log.New("component", "api").With("request_id", requestID)
logger.Info("processing request")
```

## 推荐的设计模式

### 模式一：Context-Based Logger（最推荐）

**优势：**
- 一次创建，多次复用
- 自动包含请求上下文
- 性能最佳

**实现：**
```go
// 中间件
func LoggerMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        logger := baseLogger.WithFields(Fields{
            "request_id": uuid.New().String(),
            "method": c.Request.Method,
            "path": c.Request.URL.Path,
        })
        c.Set("logger", logger)
        c.Next()
    }
}

// 控制器
func Handler(c *gin.Context) {
    log := c.MustGet("logger").(Logger)
    log.Info("processing", Fields{"user_id": userID})
}
```

### 模式二：分层日志器

**优势：**
- 不同层级有不同的日志上下文
- 便于问题定位
- 模块化设计

**实现：**
```go
// 服务层
type UserService struct {
    logger Logger
}

func NewUserService(baseLogger Logger) *UserService {
    return &UserService{
        logger: baseLogger.WithField("service", "user"),
    }
}

func (s *UserService) GetUser(userID uint) {
    opLogger := s.logger.WithField("operation", "get_user")
    opLogger.Info("fetching user")
}
```

### 模式三：错误链追踪

**优势：**
- 完整的错误调用链
- 便于问题排查
- 上下文保持

**实现：**
```go
func ComplexOperation(log Logger) error {
    log.Info("starting operation")
    
    if err := step1(log.WithField("step", "1")); err != nil {
        return err
    }
    
    if err := step2(log.WithField("step", "2")); err != nil {
        return err
    }
    
    log.Info("operation completed")
    return nil
}
```

## 性能对比

### 高性能方案排序

1. **Uber Zap** - 零分配，最快
2. **slog** - 官方标准，性能良好
3. **Logrus** - 功能丰富，性能中等
4. **标准库 log** - 简单，性能一般

### 内存分配对比

```go
// ❌ 高分配（避免）
for i := 0; i < 1000; i++ {
    logger.WithField("iteration", i).Info("processing")
}

// ✅ 低分配（推荐）
iterLogger := logger.WithField("operation", "batch")
for i := 0; i < 1000; i++ {
    iterLogger.Info("processing", Fields{"iteration": i})
}
```

## 基于你的项目的改进建议

### 当前架构的优化

```go
// 1. 优化中间件
func RequestLoggerMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        requestID := uuid.New().String()
        
        // 创建请求级别的日志器
        requestLogger := logger.GetLogger().WithFields(logger.Fields{
            "request_id": requestID,
            "method":     c.Request.Method,
            "path":       c.Request.URL.Path,
            "client_ip":  c.ClientIP(),
            "user_agent": c.Request.UserAgent(),
        })
        
        c.Set("logger", requestLogger)
        c.Set("request_id", requestID)
        c.Header("X-Request-ID", requestID)
        
        start := time.Now()
        c.Next()
        
        // 请求完成日志
        requestLogger.WithFields(logger.Fields{
            "status_code": c.Writer.Status(),
            "duration":    time.Since(start).String(),
        }).Info("request completed")
    }
}

// 2. 简化获取方式
func GetLogger(c *gin.Context) logger.Logger {
    if loggerValue, exists := c.Get("logger"); exists {
        return loggerValue.(logger.Logger)
    }
    return logger.GetLogger() // 兜底
}

// 3. 控制器使用
func (uc *UserController) GetProfile(c *gin.Context) {
    log := GetLogger(c) // 简化获取
    
    log.Info("getting user profile")
    // ... 业务逻辑
}
```

### 推荐的最终架构

1. **保持你当前的接口设计** - 很好的抽象
2. **优化中间件** - 预创建日志器
3. **添加便捷函数** - 简化获取方式
4. **考虑升级到 slog** - 如果使用 Go 1.21+

这样既保持了你现有的设计优势，又借鉴了业界最佳实践！
