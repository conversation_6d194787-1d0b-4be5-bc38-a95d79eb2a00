package dto

import (
	"time"
)

// BaseResponse 基础响应结构体
type BaseResponse struct {
	Success   bool        `json:"success"`
	Message   string      `json:"message,omitempty"`
	Data      interface{} `json:"data,omitempty"`
	Error     *ErrorInfo  `json:"error,omitempty"`
	Timestamp int64       `json:"timestamp"`
	RequestID string      `json:"request_id,omitempty"`
}

// ErrorInfo 错误信息结构体
type ErrorInfo struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// PaginationInfo 分页信息结构体
type PaginationInfo struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int64 `json:"total_pages"`
}

// PaginatedResponse 分页响应结构体
type PaginatedResponse struct {
	BaseResponse
	Pagination *PaginationInfo `json:"pagination,omitempty"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(data interface{}, message ...string) *BaseResponse {
	response := &BaseResponse{
		Success:   true,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
	
	if len(message) > 0 && message[0] != "" {
		response.Message = message[0]
	}
	
	return response
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(code, message string, details ...string) *BaseResponse {
	errorInfo := &ErrorInfo{
		Code:    code,
		Message: message,
	}
	
	if len(details) > 0 && details[0] != "" {
		errorInfo.Details = details[0]
	}
	
	return &BaseResponse{
		Success:   false,
		Error:     errorInfo,
		Timestamp: time.Now().Unix(),
	}
}

// NewPaginatedResponse 创建分页响应
func NewPaginatedResponse(data interface{}, total int64, page, pageSize int, message ...string) *PaginatedResponse {
	totalPages := (total + int64(pageSize) - 1) / int64(pageSize)
	if totalPages < 0 {
		totalPages = 0
	}
	
	response := &PaginatedResponse{
		BaseResponse: BaseResponse{
			Success:   true,
			Data:      data,
			Timestamp: time.Now().Unix(),
		},
		Pagination: &PaginationInfo{
			Page:       page,
			PageSize:   pageSize,
			Total:      total,
			TotalPages: totalPages,
		},
	}
	
	if len(message) > 0 && message[0] != "" {
		response.Message = message[0]
	}
	
	return response
}

// WithRequestID 添加请求ID
func (r *BaseResponse) WithRequestID(requestID string) *BaseResponse {
	r.RequestID = requestID
	return r
}

// WithRequestID 为分页响应添加请求ID
func (r *PaginatedResponse) WithRequestID(requestID string) *PaginatedResponse {
	r.RequestID = requestID
	return r
}
