package common

import (
	"net/http"
	"what-to-wear/server/dto"

	"github.com/gin-gonic/gin"
)

// 错误代码常量
const (
	// 通用错误代码
	ErrCodeInvalidRequest   = "INVALID_REQUEST"
	ErrCodeUnauthorized     = "UNAUTHORIZED"
	ErrCodeForbidden        = "FORBIDDEN"
	ErrCodeNotFound         = "NOT_FOUND"
	ErrCodeConflict         = "CONFLICT"
	ErrCodeInternalServer   = "INTERNAL_SERVER_ERROR"
	ErrCodeServiceUnavailable = "SERVICE_UNAVAILABLE"
	
	// 业务错误代码
	ErrCodeUserNotFound       = "USER_NOT_FOUND"
	ErrCodeUserExists         = "USER_EXISTS"
	ErrCodeInvalidCredentials = "INVALID_CREDENTIALS"
	ErrCodeInvalidPassword    = "INVALID_PASSWORD"
	ErrCodeTokenExpired       = "TOKEN_EXPIRED"
	ErrCodeTokenInvalid       = "TOKEN_INVALID"
)

// GetRequestID 从上下文获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// Success 返回成功响应
func Success(c *gin.Context, data interface{}, message ...string) {
	response := dto.NewSuccessResponse(data, message...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusOK, response)
}

// Created 返回创建成功响应
func Created(c *gin.Context, data interface{}, message ...string) {
	response := dto.NewSuccessResponse(data, message...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusCreated, response)
}

// BadRequest 返回400错误
func BadRequest(c *gin.Context, message string, details ...string) {
	response := dto.NewErrorResponse(ErrCodeInvalidRequest, message, details...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusBadRequest, response)
}

// Unauthorized 返回401错误
func Unauthorized(c *gin.Context, message string, details ...string) {
	response := dto.NewErrorResponse(ErrCodeUnauthorized, message, details...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusUnauthorized, response)
}

// Forbidden 返回403错误
func Forbidden(c *gin.Context, message string, details ...string) {
	response := dto.NewErrorResponse(ErrCodeForbidden, message, details...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusForbidden, response)
}

// NotFound 返回404错误
func NotFound(c *gin.Context, message string, details ...string) {
	response := dto.NewErrorResponse(ErrCodeNotFound, message, details...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusNotFound, response)
}

// Conflict 返回409错误
func Conflict(c *gin.Context, message string, details ...string) {
	response := dto.NewErrorResponse(ErrCodeConflict, message, details...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusConflict, response)
}

// InternalServerError 返回500错误
func InternalServerError(c *gin.Context, message string, details ...string) {
	response := dto.NewErrorResponse(ErrCodeInternalServer, message, details...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusInternalServerError, response)
}

// Paginated 返回分页响应
func Paginated(c *gin.Context, data interface{}, total int64, page, pageSize int, message ...string) {
	response := dto.NewPaginatedResponse(data, total, page, pageSize, message...)
	response.WithRequestID(GetRequestID(c))
	c.JSON(http.StatusOK, response)
}

// Error 根据错误类型返回相应的错误响应
func Error(c *gin.Context, err error) {
	requestID := GetRequestID(c)
	
	if apiErr, ok := err.(*APIError); ok {
		var response *dto.BaseResponse
		
		switch apiErr.Code {
		case http.StatusBadRequest:
			response = dto.NewErrorResponse(ErrCodeInvalidRequest, apiErr.Message, apiErr.Details)
		case http.StatusUnauthorized:
			response = dto.NewErrorResponse(ErrCodeUnauthorized, apiErr.Message, apiErr.Details)
		case http.StatusForbidden:
			response = dto.NewErrorResponse(ErrCodeForbidden, apiErr.Message, apiErr.Details)
		case http.StatusNotFound:
			response = dto.NewErrorResponse(ErrCodeNotFound, apiErr.Message, apiErr.Details)
		case http.StatusConflict:
			response = dto.NewErrorResponse(ErrCodeConflict, apiErr.Message, apiErr.Details)
		default:
			response = dto.NewErrorResponse(ErrCodeInternalServer, apiErr.Message, apiErr.Details)
		}
		
		response.WithRequestID(requestID)
		c.JSON(apiErr.Code, response)
		return
	}
	
	// 处理业务错误
	var response *dto.BaseResponse
	var statusCode int
	
	switch err.Error() {
	case "user not found":
		response = dto.NewErrorResponse(ErrCodeUserNotFound, "用户不存在")
		statusCode = http.StatusNotFound
	case "username already exists":
		response = dto.NewErrorResponse(ErrCodeUserExists, "用户名已存在")
		statusCode = http.StatusConflict
	case "email already exists":
		response = dto.NewErrorResponse(ErrCodeUserExists, "邮箱已存在")
		statusCode = http.StatusConflict
	case "invalid username or password":
		response = dto.NewErrorResponse(ErrCodeInvalidCredentials, "用户名或密码错误")
		statusCode = http.StatusUnauthorized
	case "invalid old password":
		response = dto.NewErrorResponse(ErrCodeInvalidPassword, "原密码错误")
		statusCode = http.StatusBadRequest
	default:
		response = dto.NewErrorResponse(ErrCodeInternalServer, "服务器内部错误")
		statusCode = http.StatusInternalServerError
	}
	
	response.WithRequestID(requestID)
	c.JSON(statusCode, response)
}
