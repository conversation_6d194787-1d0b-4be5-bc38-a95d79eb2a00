/* Modern Instagram-style CSS */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.app-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

/* 认证页面容器 */
.auth-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  padding: 40px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-width: 480px;
  width: 100%;
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 标题样式 */
.app-title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  padding: 8px 0;
  line-height: 1.3;
  color: #667eea;
  text-shadow: 0 2px 4px rgba(102, 126, 234, 0.2);
}

/* 如果想要渐变效果，可以使用这个版本 */
.app-title-gradient {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 8px;
  padding: 8px 0;
  line-height: 1.3;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: #667eea; /* 回退颜色 */
}

.page-subtitle {
  text-align: center;
  font-size: 16px;
  color: #666;
  margin-bottom: 32px;
  font-weight: 400;
}

/* 表单样式 */
.form-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-title::before {
  content: '';
  width: 4px;
  height: 18px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px;
}

/* 浮动标签输入框组 */
.form-group {
  margin-bottom: 24px;
  position: relative;
}

.form-input-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 20px 20px 12px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 400;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
}

.form-input:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-input:focus + .form-label,
.form-input:not(:placeholder-shown) + .form-label {
  transform: translateY(-8px) scale(0.85);
  color: #667eea;
  background: white;
  padding: 0 8px;
}

.form-label {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 16px;
  font-weight: 400;
  color: #999;
  pointer-events: none;
  transition: all 0.3s ease;
  background: transparent;
  z-index: 1;
}

/* 特殊输入类型样式（下拉框、日期、数字等） */
.form-group-special {
  margin-bottom: 20px;
  position: relative;
}

.form-label-special {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #667eea;
  margin-bottom: 6px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.form-input-special {
  width: 100%;
  padding: 14px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 400;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
  color: #333;
}

.form-input-special:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.form-input-special:disabled {
  background-color: #f8f9fa;
  color: #6c757d;
  cursor: not-allowed;
}

/* 下拉框特殊样式 */
.form-input-special[type="date"] {
  color-scheme: light;
}

.form-input-special[type="date"]::-webkit-calendar-picker-indicator {
  background-color: #667eea;
  border-radius: 3px;
  cursor: pointer;
}

/* 数字输入框样式 */
.form-input-special[type="number"] {
  -moz-appearance: textfield;
}

.form-input-special[type="number"]::-webkit-outer-spin-button,
.form-input-special[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* 传统标签样式（备用） */
.form-label-traditional {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #555;
  margin-bottom: 8px;
}

.form-input-traditional {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 400;
  background: #fff;
  transition: all 0.3s ease;
  outline: none;
}

.form-input::placeholder {
  color: #aaa;
  font-weight: 400;
}

.form-input:disabled {
  background: #f8f9fa;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 两列布局 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 8px;
  align-items: start;
}

/* 按钮样式 */
.btn {
  width: 100%;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  outline: none;
  position: relative;
  overflow: hidden;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background: transparent;
  color: #667eea;
  border: 2px solid #667eea;
}

.btn-secondary:hover {
  background: #667eea;
  color: white;
  transform: translateY(-1px);
}

/* 链接按钮 */
.link-btn {
  background: none;
  border: none;
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 6px;
}

.link-btn:hover {
  background: rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

/* 消息提示 */
.message {
  margin-top: 20px;
  padding: 16px 20px;
  border-radius: 12px;
  text-align: center;
  font-weight: 500;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-success {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(79, 172, 254, 0.3);
}

.message-error {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

/* 页面切换区域 */
.switch-page {
  text-align: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e1e5e9;
  color: #666;
}

/* 主页面样式 - 现代化Instagram风格 */
.main-container {
  background: #fafafa;
  min-height: 100vh;
  padding: 0;
}

/* 顶部导航栏 */
.main-header {
  background: #ffffff;
  border-bottom: 1px solid #e1e5e9;
  padding: 16px 0;
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(10px);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.app-logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 24px;
}

.app-name {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.user-greeting {
  color: #8e8e8e;
  font-size: 14px;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 18px;
  color: #262626;
}

.header-btn:hover {
  background: #f5f5f5;
}

.logout-btn {
  background: #262626;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  background: #1a1a1a;
  transform: translateY(-1px);
}

/* 主内容区域 */
.main-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 32px 24px;
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr;
  gap: 32px;
}

/* 今日概览 */
.today-overview {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e1e5e9;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.overview-header h2 {
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.date {
  color: #8e8e8e;
  font-size: 14px;
  font-weight: 400;
}

/* 天气卡片 */
.weather-card {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e8ecf7;
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.weather-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.weather-icon {
  font-size: 24px;
}

.weather-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.weather-main {
  display: flex;
  align-items: center;
  gap: 16px;
}

.temperature {
  font-size: 36px;
  font-weight: 700;
  color: #262626;
}

.weather-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.condition {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.humidity {
  font-size: 14px;
  color: #8e8e8e;
}

.weather-placeholder {
  text-align: center;
  color: #8e8e8e;
  padding: 20px;
}

/* 穿搭建议 */
.outfit-suggestion {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e1e5e9;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.suggestion-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.refresh-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #f5f5f5;
}

.suggestion-content {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px;
}

.suggestion-text p {
  margin: 0 0 16px 0;
  color: #262626;
  font-size: 14px;
}

.outfit-list {
  list-style: none;
  padding: 0;
  margin: 16px 0;
}

.outfit-list li {
  padding: 8px 0;
  color: #262626;
  font-size: 14px;
  position: relative;
  padding-left: 20px;
}

.outfit-list li::before {
  content: '•';
  color: #667eea;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.tip {
  margin: 16px 0 0 0 !important;
  color: #8e8e8e !important;
  font-size: 13px !important;
}

/* 快捷功能 */
.quick-actions {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e1e5e9;
}

.quick-actions h3 {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 20px 0;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.action-card {
  background: #fafafa;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.action-card:hover {
  background: #f5f5f5;
  transform: translateY(-2px);
  border-color: #d1d5db;
}

.action-card.primary {
  background: #262626;
  color: white;
  border-color: #262626;
}

.action-card.primary:hover {
  background: #1a1a1a;
}

.action-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.action-title {
  font-weight: 600;
  font-size: 14px;
  color: inherit;
}

.action-desc {
  font-size: 12px;
  color: #8e8e8e;
  margin-top: 4px;
}

.action-card.primary .action-desc {
  color: #d1d5db;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .auth-container {
    padding: 24px;
    margin: 10px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .header-content {
    padding: 0 16px;
  }

  .header-left {
    gap: 16px;
  }

  .app-name {
    font-size: 18px;
  }

  .user-greeting {
    display: none;
  }

  .main-content {
    padding: 24px 16px;
  }

  .content-wrapper {
    gap: 24px;
  }

  .overview-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .weather-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .action-card {
    padding: 16px;
  }

  .profile-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .actions-grid {
    grid-template-columns: 1fr;
  }

  .header-right {
    gap: 8px;
  }

  .logout-btn {
    padding: 6px 12px;
    font-size: 13px;
  }
}

/* 最近活动 */
.recent-activity {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e1e5e9;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.view-all-btn {
  background: none;
  border: none;
  color: #667eea;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.view-all-btn:hover {
  background: rgba(102, 126, 234, 0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: #f8f9fa;
}

.activity-icon {
  font-size: 20px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border-radius: 10px;
}

.activity-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.activity-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.activity-time {
  font-size: 12px;
  color: #8e8e8e;
}

/* 个人信息卡片 */
.profile-summary {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e1e5e9;
}

.profile-summary h3 {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 20px 0;
}

.profile-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.profile-avatar {
  width: 48px;
  height: 48px;
  background: #e1e5e9;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.profile-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.profile-info h4 {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin: 0;
}

.profile-id {
  font-size: 12px;
  color: #8e8e8e;
}

.edit-profile-btn {
  background: none;
  border: 1px solid #e1e5e9;
  color: #262626;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-profile-btn:hover {
  background: #f5f5f5;
  border-color: #d1d5db;
}

/* 加载动画 */
.main-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: #fafafa;
  gap: 16px;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e1e5e9;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.main-loading p {
  font-size: 16px;
  color: #8e8e8e;
  margin: 0;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
