<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="fac17ab5-caaa-4970-8241-e7862f42ded5" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/dto/auth_dto.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/dto/outfit_dto.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/dto/user_dto.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/services/auth_interfaces.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/services/outfit_interfaces.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/services/user_interfaces.go" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/services/weather_interfaces.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../client/what-to-wear-client/src-tauri/Cargo.toml" beforeDir="false" afterPath="$PROJECT_DIR$/../client/what-to-wear-client/src-tauri/Cargo.toml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/controllers/auth.go" beforeDir="false" afterPath="$PROJECT_DIR$/controllers/auth.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/logger/logger.go" beforeDir="false" afterPath="$PROJECT_DIR$/logger/logger.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/auth_service.go" beforeDir="false" afterPath="$PROJECT_DIR$/services/auth_service.go" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/services/interfaces.go" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/services/user_service.go" beforeDir="false" afterPath="$PROJECT_DIR$/services/user_service.go" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://D:/RuntimeLibs/Go/go" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="ProjectColorInfo"><![CDATA[{
  "associatedIndex": 0
}]]></component>
  <component name="ProjectId" id="30xYrJ856w1YUjYtq7UdBoLAINl" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Go Build.go build what-to-wear/server.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.GoLinterPluginOnboarding": "true",
    "RunOnceActivity.GoLinterPluginStorageMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "git-widget-placeholder": "main",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "E:/what-to-wear/server",
    "node.js.detected.package.eslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "go.vgo"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="RunManager">
    <configuration name="go build what-to-wear/server" type="GoApplicationRunConfiguration" factoryName="Go Application" temporary="true" nameIsGenerated="true">
      <module name="server" />
      <working_directory value="$PROJECT_DIR$" />
      <kind value="PACKAGE" />
      <package value="what-to-wear/server" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$/main.go" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Go Build.go build what-to-wear/server" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-3b128438d3f6-4b567d62c776-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-251.26094.127" />
        <option value="bundled-js-predefined-d6986cc7102b-b26f3e71634d-JavaScript-GO-251.26094.127" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="fac17ab5-caaa-4970-8241-e7862f42ded5" name="Changes" comment="" />
      <created>1754570888061</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754570888061</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>