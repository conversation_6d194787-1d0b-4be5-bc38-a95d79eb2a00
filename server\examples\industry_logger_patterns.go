package examples

import (
	"time"
	"what-to-wear/server/logger"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// 业界流行的日志设计模式示例

// 1. Context-Based Logger（最推荐）
// 类似 Uber Zap + Gin 的设计
func ContextLoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := uuid.New().String()
		startTime := time.Now()
		
		// 创建带请求上下文的日志器
		requestLogger := logger.GetLogger().WithFields(logger.Fields{
			"request_id": requestID,
			"method":     c.Request.Method,
			"path":       c.Request.URL.Path,
			"client_ip":  c.ClientIP(),
		})
		
		// 存储到 context
		c.Set("logger", requestLogger)
		c.Set("request_id", requestID)
		c.Set("start_time", startTime)
		
		c.Next()
		
		// 请求结束日志
		duration := time.Since(startTime)
		requestLogger.WithFields(logger.Fields{
			"status_code": c.Writer.Status(),
			"duration":    duration.String(),
		}).Info("request completed")
	}
}

// 控制器中的使用方式
func ExampleHandler(c *gin.Context) {
	// 获取预创建的日志器
	log := c.MustGet("logger").(logger.Logger)
	
	userID := c.GetUint("user_id")
	
	log.Info("processing user request", logger.Fields{
		"user_id": userID,
		"action":  "get_profile",
	})
	
	// 业务逻辑...
	
	log.Info("request processed successfully", logger.Fields{
		"user_id": userID,
		"result":  "success",
	})
}

// 2. Kubernetes 风格的日志器
// 使用结构化字段和对象引用
func KubernetesStyleLogging(c *gin.Context) {
	log := c.MustGet("logger").(logger.Logger)
	
	// 类似 klog.InfoS 的方式
	log.Info("user operation", logger.Fields{
		"user":      "john_doe",
		"operation": "login",
		"namespace": "default",
		"resource":  "user",
	})
}

// 3. Prometheus 风格的日志器
// 使用组件化的日志器
func PrometheusStyleLogging() {
	// 为特定组件创建日志器
	apiLogger := logger.GetLogger().WithFields(logger.Fields{
		"component": "api",
		"version":   "v1",
	})
	
	authLogger := apiLogger.WithField("module", "auth")
	
	authLogger.Info("authentication started")
	authLogger.Error("authentication failed", logger.Fields{
		"reason": "invalid_credentials",
	})
}

// 4. 分层日志器设计
// 不同层级使用不同的日志器
type ServiceWithLogger struct {
	logger logger.Logger
}

func NewServiceWithLogger(baseLogger logger.Logger) *ServiceWithLogger {
	return &ServiceWithLogger{
		logger: baseLogger.WithField("service", "user_service"),
	}
}

func (s *ServiceWithLogger) GetUser(userID uint) error {
	// 为特定操作创建子日志器
	opLogger := s.logger.WithFields(logger.Fields{
		"operation": "get_user",
		"user_id":   userID,
	})
	
	opLogger.Info("fetching user from database")
	
	// 模拟数据库操作
	if userID == 0 {
		opLogger.Error("invalid user ID")
		return nil
	}
	
	opLogger.Info("user fetched successfully")
	return nil
}

// 5. 错误链追踪（类似 Grafana）
func ErrorChainLogging(c *gin.Context) {
	log := c.MustGet("logger").(logger.Logger)
	
	// 操作开始
	log.Info("starting complex operation")
	
	// 步骤1
	stepLogger := log.WithField("step", "validation")
	stepLogger.Info("validating input")
	
	// 步骤2
	stepLogger = log.WithField("step", "processing")
	stepLogger.Info("processing data")
	
	// 错误处理
	if err := someOperation(); err != nil {
		stepLogger.WithField("error", err.Error()).Error("operation failed")
		return
	}
	
	log.Info("complex operation completed successfully")
}

func someOperation() error {
	return nil // 模拟函数
}

// 6. 性能监控日志（类似 Docker）
func PerformanceLogging(c *gin.Context) {
	log := c.MustGet("logger").(logger.Logger)
	start := time.Now()
	
	defer func() {
		duration := time.Since(start)
		log.WithFields(logger.Fields{
			"duration_ms": duration.Milliseconds(),
			"memory_mb":   getMemoryUsage(), // 假设的函数
		}).Info("operation performance metrics")
	}()
	
	// 业务逻辑...
	time.Sleep(100 * time.Millisecond) // 模拟耗时操作
}

func getMemoryUsage() int64 {
	return 128 // 模拟内存使用量
}

// 7. 业界推荐的日志级别使用
func LogLevelBestPractices(c *gin.Context) {
	log := c.MustGet("logger").(logger.Logger)
	
	// Debug: 详细的调试信息
	log.Debug("detailed debug info", logger.Fields{
		"variable": "value",
		"state":    "processing",
	})
	
	// Info: 一般信息，业务流程
	log.Info("user login successful", logger.Fields{
		"user_id": 123,
	})
	
	// Warn: 警告，不影响功能但需要注意
	log.Warn("deprecated API used", logger.Fields{
		"api":     "/old/endpoint",
		"user_id": 123,
	})
	
	// Error: 错误，影响功能
	log.Error("database connection failed", logger.Fields{
		"error":    "connection timeout",
		"database": "users",
	})
}
