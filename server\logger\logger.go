package logger

import (
	"context"
	"sync"
)

var (
	defaultLogger Logger
	once          sync.Once
)

// Init 初始化全局日志器
func Init(config *Config) {
	once.Do(func() {
		if config == nil {
			config = DefaultConfig()
		}
		defaultLogger = NewLogrusLogger(config)
	})
}

// GetLogger 获取全局日志器
func GetLogger() Logger {
	if defaultLogger == nil {
		Init(DefaultConfig())
	}
	return defaultLogger
}

// 全局日志方法，方便直接使用

// Debug 调试日志
func Debug(msg string, fields ...Fields) {
	GetLogger().Debug(msg, fields...)
}

// Info 信息日志
func Info(msg string, fields ...Fields) {
	GetLogger().Info(msg, fields...)
}

// Warn 警告日志
func Warn(msg string, fields ...Fields) {
	GetLogger().Warn(msg, fields...)
}

// Error 错误日志
func Error(msg string, fields ...Fields) {
	GetLogger().Error(msg, fields...)
}

// Fatal 致命错误日志
func Fatal(msg string, fields ...Fields) {
	GetLogger().Fatal(msg, fields...)
}

// DebugContext 带上下文的调试日志
func DebugContext(ctx context.Context, msg string, fields ...Fields) {
	GetLogger().DebugContext(ctx, msg, fields...)
}

// InfoContext 带上下文的信息日志
func InfoContext(ctx context.Context, msg string, fields ...Fields) {
	GetLogger().InfoContext(ctx, msg, fields...)
}

// WarnContext 带上下文的警告日志
func WarnContext(ctx context.Context, msg string, fields ...Fields) {
	GetLogger().WarnContext(ctx, msg, fields...)
}

// ErrorContext 带上下文的错误日志
func ErrorContext(ctx context.Context, msg string, fields ...Fields) {
	GetLogger().ErrorContext(ctx, msg, fields...)
}

// ErrorWithErr 带错误的错误日志
func ErrorWithErr(err error, msg string, fields ...Fields) {
	GetLogger().ErrorWithErr(err, msg, fields...)
}

// WarnWithErr 带错误的警告日志
func WarnWithErr(err error, msg string, fields ...Fields) {
	GetLogger().WarnWithErr(err, msg, fields...)
}

// WithFields 创建带字段的子日志器
func WithFields(fields Fields) Logger {
	return GetLogger().WithFields(fields)
}

// WithField 创建带单个字段的子日志器
func WithField(key string, value interface{}) Logger {
	return GetLogger().WithField(key, value)
}

// WithError 创建带错误的子日志器
func WithError(err error) Logger {
	return GetLogger().WithError(err)
}

// WithContext 创建带上下文的子日志器
func WithContext(ctx context.Context) Logger {
	return GetLogger().WithContext(ctx)
}

// SetLevel 设置日志级别
func SetLevel(level LogLevel) {
	GetLogger().SetLevel(level)
}

// 以下是自动包含 request_id 的便捷函数

// FromGinContext 从 Gin Context 创建带 request_id 的日志器
func FromGinContext(c interface{}) Logger {
	// 使用 interface{} 避免循环导入，在运行时进行类型断言
	if ginCtx, ok := c.(interface{ GetString(string) string }); ok {
		if requestID := ginCtx.GetString("request_id"); requestID != "" {
			return GetLogger().WithField("request_id", requestID)
		}
	}
	return GetLogger()
}

// WithRequestID 创建带 request_id 的日志器
func WithRequestID(requestID string) Logger {
	if requestID == "" {
		return GetLogger()
	}
	return GetLogger().WithField("request_id", requestID)
}

// 带 request_id 的便捷日志函数

// DebugWithRequestID 带 request_id 的调试日志
func DebugWithRequestID(requestID, msg string, fields ...Fields) {
	WithRequestID(requestID).Debug(msg, fields...)
}

// InfoWithRequestID 带 request_id 的信息日志
func InfoWithRequestID(requestID, msg string, fields ...Fields) {
	WithRequestID(requestID).Info(msg, fields...)
}

// WarnWithRequestID 带 request_id 的警告日志
func WarnWithRequestID(requestID, msg string, fields ...Fields) {
	WithRequestID(requestID).Warn(msg, fields...)
}

// ErrorWithRequestID 带 request_id 的错误日志
func ErrorWithRequestID(requestID, msg string, fields ...Fields) {
	WithRequestID(requestID).Error(msg, fields...)
}

// 从 Gin Context 直接记录日志的便捷函数

// DebugFromContext 从 Gin Context 记录调试日志（自动包含 request_id）
func DebugFromContext(c interface{}, msg string, fields ...Fields) {
	FromGinContext(c).Debug(msg, fields...)
}

// InfoFromContext 从 Gin Context 记录信息日志（自动包含 request_id）
func InfoFromContext(c interface{}, msg string, fields ...Fields) {
	FromGinContext(c).Info(msg, fields...)
}

// WarnFromContext 从 Gin Context 记录警告日志（自动包含 request_id）
func WarnFromContext(c interface{}, msg string, fields ...Fields) {
	FromGinContext(c).Warn(msg, fields...)
}

// ErrorFromContext 从 Gin Context 记录错误日志（自动包含 request_id）
func ErrorFromContext(c interface{}, msg string, fields ...Fields) {
	FromGinContext(c).Error(msg, fields...)
}
