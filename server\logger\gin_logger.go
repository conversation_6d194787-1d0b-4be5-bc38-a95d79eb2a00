package logger

import (
	"github.com/gin-gonic/gin"
)

// GinLogger 专门用于 Gin 框架的日志器
type GinLogger struct {
	logger    Logger
	requestID string
}

// NewGinLogger 创建 Gin 日志器
func NewGinLogger(c *gin.Context) *GinLogger {
	requestID := c.GetString("request_id")
	return &GinLogger{
		logger:    GetLogger().WithField("request_id", requestID),
		requestID: requestID,
	}
}

// Debug 调试日志
func (g *GinLogger) Debug(msg string, fields ...Fields) {
	g.logger.Debug(msg, fields...)
}

// Info 信息日志
func (g *GinLogger) Info(msg string, fields ...Fields) {
	g.logger.Info(msg, fields...)
}

// Warn 警告日志
func (g *GinLogger) Warn(msg string, fields ...Fields) {
	g.logger.Warn(msg, fields...)
}

// Error 错误日志
func (g *GinLogger) Error(msg string, fields ...Fields) {
	g.logger.Error(msg, fields...)
}

// Fatal 致命错误日志
func (g *GinLogger) Fatal(msg string, fields ...Fields) {
	g.logger.Fatal(msg, fields...)
}

// ErrorWithErr 带错误的错误日志
func (g *GinLogger) ErrorWithErr(err error, msg string, fields ...Fields) {
	g.logger.ErrorWithErr(err, msg, fields...)
}

// WarnWithErr 带错误的警告日志
func (g *GinLogger) WarnWithErr(err error, msg string, fields ...Fields) {
	g.logger.WarnWithErr(err, msg, fields...)
}

// WithFields 创建带字段的子日志器
func (g *GinLogger) WithFields(fields Fields) *GinLogger {
	return &GinLogger{
		logger:    g.logger.WithFields(fields),
		requestID: g.requestID,
	}
}

// WithField 创建带单个字段的子日志器
func (g *GinLogger) WithField(key string, value interface{}) *GinLogger {
	return &GinLogger{
		logger:    g.logger.WithField(key, value),
		requestID: g.requestID,
	}
}

// WithError 创建带错误的子日志器
func (g *GinLogger) WithError(err error) *GinLogger {
	return &GinLogger{
		logger:    g.logger.WithError(err),
		requestID: g.requestID,
	}
}

// GetRequestID 获取请求ID
func (g *GinLogger) GetRequestID() string {
	return g.requestID
}

// 全局便捷函数

// L 从 Gin Context 创建日志器的简短函数
func L(c *gin.Context) *GinLogger {
	return NewGinLogger(c)
}

// Log 从 Gin Context 创建日志器（别名）
func Log(c *gin.Context) *GinLogger {
	return NewGinLogger(c)
}

// FromContext 从 Gin Context 创建日志器（更明确的名称）
func FromContext(c *gin.Context) *GinLogger {
	return NewGinLogger(c)
}
