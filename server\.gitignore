# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local
.env.production

# Database data
postgres_data/
*.db
*.sqlite
*.sqlite3

# Logs
*.log
logs/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
tmp/
temp/
*.tmp

# Build output
dist/
build/
bin/

# Air live reload tool
tmp/

# Coverage reports
coverage.out
coverage.html

# Profiling data
*.prof
*.pprof

# Local configuration files
config.local.yaml
config.local.json