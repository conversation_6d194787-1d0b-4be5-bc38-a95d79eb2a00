package examples

import (
	"what-to-wear/server/common"
	"what-to-wear/server/dto"

	"github.com/gin-gonic/gin"
)

// 这个文件展示了如何使用统一的响应结构体

// ExampleController 示例控制器
type ExampleController struct{}

// GetUser 获取用户信息示例
func (ec *ExampleController) GetUser(c *gin.Context) {
	// 模拟获取用户数据
	user := &dto.UserProfile{
		ID:       1,
		Username: "john_doe",
		Email:    "<EMAIL>",
		Nickname: "<PERSON>",
		Gender:   "male",
	}

	// 使用统一的成功响应
	common.Success(c, user, "获取用户信息成功")
}

// GetUserList 获取用户列表示例（分页）
func (ec *ExampleController) GetUserList(c *gin.Context) {
	// 模拟分页数据
	users := []dto.UserProfile{
		{ID: 1, Username: "user1", Email: "<EMAIL>"},
		{ID: 2, Username: "user2", Email: "<EMAIL>"},
	}

	// 使用分页响应
	common.Paginated(c, users, 100, 1, 10, "获取用户列表成功")
}

// CreateUser 创建用户示例
func (ec *ExampleController) CreateUser(c *gin.Context) {
	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		// 使用统一的错误响应
		common.BadRequest(c, "请求参数无效", err.Error())
		return
	}

	// 模拟创建用户
	user := &dto.UserProfile{
		ID:       123,
		Username: req.Username,
		Email:    req.Email,
		Nickname: req.Nickname,
	}

	// 使用创建成功响应
	common.Created(c, user, "用户创建成功")
}

// HandleError 错误处理示例
func (ec *ExampleController) HandleError(c *gin.Context) {
	// 不同类型的错误响应示例

	// 400 错误
	common.BadRequest(c, "请求参数错误", "用户名不能为空")

	// 401 错误
	common.Unauthorized(c, "未授权访问", "请先登录")

	// 403 错误
	common.Forbidden(c, "权限不足", "您没有访问此资源的权限")

	// 404 错误
	common.NotFound(c, "资源不存在", "用户不存在")

	// 409 错误
	common.Conflict(c, "资源冲突", "用户名已存在")

	// 500 错误
	common.InternalServerError(c, "服务器内部错误", "数据库连接失败")
}

// 响应格式示例：

// 成功响应格式：
// {
//   "success": true,
//   "message": "获取用户信息成功",
//   "data": {
//     "id": 1,
//     "username": "john_doe",
//     "email": "<EMAIL>",
//     "nickname": "John",
//     "gender": "male"
//   },
//   "timestamp": 1672531200,
//   "request_id": "550e8400-e29b-41d4-a716-446655440000"
// }

// 错误响应格式：
// {
//   "success": false,
//   "error": {
//     "code": "INVALID_REQUEST",
//     "message": "请求参数无效",
//     "details": "用户名不能为空"
//   },
//   "timestamp": 1672531200,
//   "request_id": "550e8400-e29b-41d4-a716-446655440000"
// }

// 分页响应格式：
// {
//   "success": true,
//   "message": "获取用户列表成功",
//   "data": [
//     {"id": 1, "username": "user1", "email": "<EMAIL>"},
//     {"id": 2, "username": "user2", "email": "<EMAIL>"}
//   ],
//   "pagination": {
//     "page": 1,
//     "page_size": 10,
//     "total": 100,
//     "total_pages": 10
//   },
//   "timestamp": 1672531200,
//   "request_id": "550e8400-e29b-41d4-a716-446655440000"
// }
