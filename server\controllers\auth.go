package controllers

import (
	"what-to-wear/server/common"
	"what-to-wear/server/dto"
	"what-to-wear/server/logger"
	"what-to-wear/server/services"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	authService services.AuthService
}

// NewAuthController 创建认证控制器实例
func NewAuthController(authService services.AuthService) *AuthController {
	return &AuthController{
		authService: authService,
	}
}

// Register 用户注册
func (ac *AuthController) Register(c *gin.Context) {
	// 在请求开始时创建一次带 request_id 的日志器
	log := logger.FromGinContext(c)

	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn("Invalid registration request", logger.Fields{
			"error": err.<PERSON>rror(),
		})
		common.HandleError(c, common.ErrInvalidRequest)
		return
	}

	log.Info("Processing user registration", logger.Fields{
		"username": req.Username,
		"email":    req.Email,
	})

	user, err := ac.authService.Register(&req)
	if err != nil {
		log.Error("User registration failed", logger.Fields{
			"username": req.Username,
			"error":    err.Error(),
		})
		common.HandleError(c, err)
		return
	}

	log.Info("User registration successful", logger.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	})

	// 构造用户资料响应
	userProfile := &dto.UserProfile{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Nickname: user.Nickname,
		Gender:   user.Gender,
		Height:   user.Height,
		Weight:   user.Weight,
	}

	// 处理生日日期转换
	if user.BirthDate != nil {
		userProfile.BirthDate = user.BirthDate.Format("2006-01-02")
	}

	common.CreatedResponse(c, userProfile, "用户注册成功")
}

// Login 用户登录
func (ac *AuthController) Login(c *gin.Context) {
	// 在请求开始时获取预创建的日志器（只创建一次）
	log := logger.FromGinContext(c)

	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn("Invalid login request", logger.Fields{
			"error": err.Error(),
		})
		common.HandleError(c, common.ErrInvalidRequest)
		return
	}

	log.Info("Processing user login", logger.Fields{
		"username": req.Username,
	})

	token, err := ac.authService.Login(req.Username, req.Password)
	if err != nil {
		log.Error("User login failed", logger.Fields{
			"username": req.Username,
			"error":    err.Error(),
		})
		common.HandleError(c, err)
		return
	}

	log.Info("User login successful", logger.Fields{
		"username": req.Username,
	})

	// 构造登录响应
	response := &dto.LoginResponse{
		Token: token,
		// 这里可以添加用户信息，需要从服务层获取
	}

	common.SuccessResponse(c, response, "登录成功")
}
