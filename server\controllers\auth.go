package controllers

import (
	"what-to-wear/server/common"
	"what-to-wear/server/dto"
	"what-to-wear/server/logger"
	"what-to-wear/server/services"

	"github.com/gin-gonic/gin"
)

// AuthController 认证控制器
type AuthController struct {
	authService services.AuthService
}

// NewAuthController 创建认证控制器实例
func NewAuthController(authService services.AuthService) *AuthController {
	return &AuthController{
		authService: authService,
	}
}

// Register 用户注册
func (ac *AuthController) Register(c *gin.Context) {
	requestID := c.GetString("request_id")

	var req dto.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Warn("Invalid registration request", logger.Fields{
			"request_id": requestID,
			"error":      err.Error(),
		})
		common.BadRequest(c, "请求参数无效", err.<PERSON>rror())
		return
	}

	logger.Info("Processing user registration", logger.Fields{
		"request_id": requestID,
		"username":   req.Username,
		"email":      req.Email,
	})

	user, err := ac.authService.Register(&req)
	if err != nil {
		logger.Error("User registration failed", logger.Fields{
			"request_id": requestID,
			"username":   req.Username,
			"error":      err.Error(),
		})
		common.Error(c, err)
		return
	}

	logger.Info("User registration successful", logger.Fields{
		"request_id": requestID,
		"user_id":    user.ID,
		"username":   user.Username,
	})

	// 构造用户资料响应
	userProfile := &dto.UserProfile{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Nickname: user.Nickname,
		Gender:   user.Gender,
		Height:   user.Height,
		Weight:   user.Weight,
	}

	// 处理生日日期转换
	if user.BirthDate != nil {
		userProfile.BirthDate = user.BirthDate.Format("2006-01-02")
	}

	common.Created(c, userProfile, "用户注册成功")
}

// Login 用户登录
func (ac *AuthController) Login(c *gin.Context) {
	var req dto.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		common.BadRequest(c, "请求参数无效", err.Error())
		return
	}

	token, err := ac.authService.Login(req.Username, req.Password)
	if err != nil {
		common.Error(c, err)
		return
	}

	// 构造登录响应
	response := &dto.LoginResponse{
		Token: token,
		// 这里可以添加用户信息，需要从服务层获取
	}

	common.Success(c, response, "登录成功")
}
